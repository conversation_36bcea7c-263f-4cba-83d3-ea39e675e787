# Data Importer Class Documentation

## Overview

The `data_importer` class is a comprehensive utility for importing structured data from CSV and JSON files into database tables. It provides flexible mapping configurations, duplicate detection, batch processing, and specialized support for hilt templates.

**Namespace:** `system`  
**File:** `system/classes/data_importer.class.php`  
**Dependencies:** `system\database`

## Class Properties

### Static Properties

```php
public static string $log_target = "data_importer";
```
- **Purpose:** Defines the logging target for all class operations
- **Usage:** Can be modified to change logging destination

## Core Methods

### 1. import_csv_to_hilt_table()

Simplified method for importing CSV data directly into hilt tables.

```php
public static function import_csv_to_hilt_table(
    string $table_name, 
    string $csv_data_or_path, 
    bool $is_file_path = true, 
    bool $replace_all = false
): array
```

**Parameters:**
- `$table_name` - Target database table name
- `$csv_data_or_path` - CSV file path or raw CSV data string
- `$is_file_path` - Whether second parameter is file path (true) or data string (false)
- `$replace_all` - Whether to truncate existing table data before import

**Returns:** Array with import results
- `success` - Boolean indicating success/failure
- `message` - Descriptive message
- `imported_count` - Number of records imported

**Features:**
- Automatic table truncation option
- Handles both file paths and raw CSV strings
- Creates temporary files for string data processing
- Stores data as JSON in `data_json` column with MD5 hash in `data_hash` column
- Skips malformed rows automatically

### 2. import_data_into_database()

Main method for importing data with complex mapping configurations.

```php
public static function import_data_into_database(
    array $mapping, 
    string $file_path, 
    string $file_type = 'csv', 
    array $unique_hash = [], 
    bool $debug = true, 
    string $log_target = "main"
): array
```

**Parameters:**
- `$mapping` - Complex mapping configuration array (see Mapping Configuration section)
- `$file_path` - Path to data file
- `$file_type` - File type: 'csv' or 'json'
- `$unique_hash` - Column names/paths for generating unique hashes
- `$debug` - Enable detailed debug information
- `$log_target` - Custom logging target

**Returns:** Array with detailed import results

### 3. import_csv_into_database()

Convenience wrapper for CSV imports with mapping.

```php
public static function import_csv_into_database(
    array $mapping, 
    string $csv_file_path, 
    array $unique_hash = [], 
    bool $debug = true, 
    string $log_target = "data_importer"
): array
```

## File Reading Methods

### 4. read_csv_file()

```php
public static function read_csv_file(string $csv_file_path): array
```

**Purpose:** Reads and parses CSV files
**Returns:** 
- `header` - Array of column headers
- `rows` - Array of data rows
- `error` - Error message if file reading fails

### 5. read_json_file()

```php
public static function read_json_file(string $json_file_path): array
```

**Purpose:** Reads and parses JSON files
**Returns:** Parsed JSON data or error array

## Data Processing Methods

### 6. process_csv_data()

```php
private static function process_csv_data(
    array $mapping, 
    array $header, 
    array $rows, 
    array $unique_hash = [], 
    bool $debug = true
): array
```

**Purpose:** Processes CSV data according to mapping configuration
**Features:**
- Handles multiple database table mappings
- Processes extra fields with special tags
- Generates unique hashes for duplicate detection
- Executes database operations with error handling

### 7. process_json_data()

```php
public static function process_json_data(
    array $mapping, 
    array $json_data, 
    array $unique_hash = [], 
    bool $debug = true, 
    string $log_target_old = "main"
): array
```

**Purpose:** Processes JSON data with support for nested structures
**Features:**
- Handles both array and non-array JSON data
- Flattens nested JSON structures using dot notation
- Supports complex data relationships (line items, bill plans)

## Database Operations

### 8. execute_database_operation()

```php
private static function execute_database_operation(
    string $table, 
    string $key, 
    array $data
): array
```

**Purpose:** Executes INSERT...ON DUPLICATE KEY UPDATE operations
**Features:**
- Uses named parameters for all database operations
- Handles array values by JSON encoding
- Returns affected rows count and insert ID
- Comprehensive error handling

## Hash Management

### 9. create_unique_hash()

```php
public static function create_unique_hash(array $columns): array
```

**Purpose:** Generates unique hashes from column values
**Returns:**
- `string` - Concatenated original values
- `hash` - CRC32 hash of the string

### 10. update_database_hashes()

```php
public static function update_database_hashes(
    string $table, 
    string $hash_column, 
    array $source_columns, 
    string $string_column = null, 
    int $batch_size = 100
): array
```

**Purpose:** Updates existing database records with new hash values
**Features:**
- Batch processing for large datasets
- Progress reporting
- Error tracking and recovery

### 11. update_autodesk_catalog_hashes()

```php
public static function update_autodesk_catalog_hashes(int $batch_size = 100): array
```

**Purpose:** Specialized method for updating Autodesk catalog hashes
**Features:**
- Updates main catalog table and related tables
- Maintains referential integrity
- Hash mapping for related table updates

## Utility Methods

### 12. process_extra_fields()

```php
private static function process_extra_fields(
    array $query_array, 
    array $extra_fields, 
    array $data, 
    array $unique_hash, 
    array $insert_ids
): array
```

**Purpose:** Processes special field configurations in mappings
**Supported Tags:**
- `<unique_hash>` - Generates unique hash from specified columns
- `<hash_string>` - Returns concatenated string used for hash
- `<group_insert_id>group_name` - References insert ID from another group
- `<json_encode>path` - JSON encodes value at specified path

### 13. flatten_nested_array()

```php
private static function flatten_nested_array(array $data, string $prefix = ''): array
```

**Purpose:** Converts nested arrays to flat arrays with dot notation keys
**Example:** `['user' => ['name' => 'John']]` becomes `['user.name' => 'John']`

### 14. get_value_from_path()

```php
private static function get_value_from_path(array $data, string $path): mixed
```

**Purpose:** Retrieves values from flattened arrays using dot notation paths
**Example:** `get_value_from_path($data, 'user.profile.email')`

### 15. format_import_result()

```php
private static function format_import_result(int $row_number, array $query_results): string
```

**Purpose:** Formats import results for human-readable output
**Returns:** HTML-formatted string with operation results

## Mapping Configuration

The mapping configuration is a multi-dimensional array that defines how source data maps to database tables and columns.

### Basic Structure

```php
$mapping = [
    'group_name' => [
        'table' => 'database_table_name',
        'key' => 'primary_key_column',
        'columns' => [
            'source_field' => 'db_column',
            'another_field' => 'another_column'
        ],
        'extra' => [
            'db_column' => '<special_tag>parameter'
        ]
    ]
];
```

### CSV Mapping Example

```php
$csv_mapping = [
    'products' => [
        'table' => 'products',
        'key' => 'product_id',
        'columns' => [
            'Name' => 'product_name',
            'Price' => 'price',
            'SKU' => 'sku',
            'Category' => 'category'
        ],
        'extra' => [
            'unique_hash' => '<unique_hash>',
            'hash_string' => '<hash_string>',
            'import_date' => date('Y-m-d H:i:s')
        ]
    ]
];
```

### JSON Mapping Example

```php
$json_mapping = [
    'customers' => [
        'table' => 'customers',
        'key' => 'customer_id',
        'columns' => [
            'customer.name' => 'customer_name',
            'customer.email' => 'email',
            'customer.address.street' => 'street_address',
            'customer.address.city' => 'city'
        ]
    ],
    'orders' => [
        'table' => 'orders',
        'key' => 'order_id',
        'columns' => [
            'lineItems.orderId' => 'order_id',
            'lineItems.productId' => 'product_id',
            'lineItems.quantity' => 'quantity',
            'lineItems.price' => 'unit_price'
        ],
        'extra' => [
            'customer_id' => '<group_insert_id>customers',
            'order_data' => '<json_encode>lineItems'
        ]
    ]
];
```

### Special Tags in Extra Fields

1. **`<unique_hash>`** - Generates CRC32 hash from unique_hash columns
2. **`<hash_string>`** - Returns concatenated string used for hash generation
3. **`<group_insert_id>group_name`** - References insert ID from another mapping group
4. **`<json_encode>path`** - JSON encodes data at specified dot notation path

## Usage Examples

### Simple CSV Import to Hilt Table

```php
use system\data_importer;

// Import CSV file to hilt table
$result = data_importer::import_csv_to_hilt_table(
    'autobooks_products_data',
    '/path/to/products.csv',
    true,  // is_file_path
    false  // replace_all
);

if ($result['success']) {
    echo "Successfully imported {$result['imported_count']} records";
} else {
    echo "Import failed: {$result['message']}";
}
```

### CSV Import with Raw Data String

```php
$csv_data = "Name,Price,SKU\nProduct A,19.99,SKU001\nProduct B,29.99,SKU002";

$result = data_importer::import_csv_to_hilt_table(
    'autobooks_products_data',
    $csv_data,
    false, // is_file_path (using raw data)
    true   // replace_all (clear existing data)
);
```

### Complex CSV Import with Mapping

```php
$mapping = [
    'products' => [
        'table' => 'products_catalog',
        'key' => 'sku',
        'columns' => [
            'Product Name' => 'name',
            'Price' => 'price',
            'SKU' => 'sku',
            'Description' => 'description'
        ],
        'extra' => [
            'unique_hash' => '<unique_hash>',
            'hash_string' => '<hash_string>',
            'created_date' => date('Y-m-d H:i:s')
        ]
    ]
];

$result = data_importer::import_csv_into_database(
    $mapping,
    '/path/to/products.csv',
    ['Product Name', 'SKU'], // unique_hash columns
    true // debug mode
);
```

### JSON Import with Nested Data

```php
$mapping = [
    'subscriptions' => [
        'table' => 'autodesk_subscriptions',
        'key' => 'subscription_id',
        'columns' => [
            'subscription.id' => 'subscription_id',
            'subscription.name' => 'subscription_name',
            'subscription.status' => 'status'
        ]
    ],
    'line_items' => [
        'table' => 'subscription_line_items',
        'key' => 'line_item_id',
        'columns' => [
            'lineItems.id' => 'line_item_id',
            'lineItems.productId' => 'product_id',
            'lineItems.quantity' => 'quantity'
        ],
        'extra' => [
            'subscription_id' => '<group_insert_id>subscriptions'
        ]
    ]
];

$result = data_importer::import_data_into_database(
    $mapping,
    '/path/to/subscriptions.json',
    'json',
    ['subscription.id'],
    true,
    'subscription_import'
);
```

### Hash Management Operations

```php
// Update hashes for existing records
$result = data_importer::update_database_hashes(
    'products_catalog',           // table name
    'unique_hash',               // hash column
    ['name', 'sku', 'price'],    // source columns for hash
    'hash_string',               // string column (optional)
    50                          // batch size
);

// Update Autodesk catalog hashes specifically
$result = data_importer::update_autodesk_catalog_hashes(100);
```

## Error Handling

The class provides comprehensive error handling with detailed logging and return values.

### Return Value Structure

**Success Response:**
```php
[
    'success' => true,
    'message' => 'Detailed success message',
    'imported_count' => 150  // for hilt imports
]
```

**Error Response:**
```php
[
    'error' => 'Error description',
    'details' => 'Additional error details',
    'data' => 'Problematic data (if applicable)'
]
```

### Common Error Scenarios

1. **File Not Found**
   - CSV/JSON file doesn't exist at specified path
   - Returns: `['error' => 'Could not open CSV file.']`

2. **Invalid File Format**
   - Empty files
   - Malformed JSON
   - Returns: `['error' => 'Invalid JSON: error_message']`

3. **Database Errors**
   - Connection issues
   - Invalid table/column names
   - Constraint violations
   - Returns: `['error' => 'Database error details']`

4. **Mapping Configuration Errors**
   - Invalid mapping structure
   - Missing required fields
   - Returns: `['error' => 'Mapping Invalid']`

## Best Practices

### 1. File Validation
```php
// Always check if file exists before import
if (!file_exists($csv_path)) {
    throw new Exception("CSV file not found: {$csv_path}");
}

// Validate file size for large imports
$file_size = filesize($csv_path);
if ($file_size > 50 * 1024 * 1024) { // 50MB limit
    echo "Warning: Large file detected. Consider batch processing.";
}
```

### 2. Memory Management
```php
// For large datasets, use batch processing
$batch_size = 1000;
$result = data_importer::update_database_hashes(
    'large_table',
    'hash_column',
    ['col1', 'col2'],
    null,
    $batch_size
);
```

### 3. Logging Configuration
```php
// Set custom log target for specific imports
data_importer::$log_target = "product_import_" . date('Y-m-d');

// Enable debug mode for troubleshooting
$result = data_importer::import_data_into_database(
    $mapping,
    $file_path,
    'csv',
    $unique_hash,
    true  // debug enabled
);
```

### 4. Data Validation
```php
// Validate mapping before import
if (!is_array($mapping) || empty($mapping)) {
    throw new Exception("Invalid mapping configuration");
}

// Check required mapping fields
foreach ($mapping as $group_name => $config) {
    if (!isset($config['table']) || !isset($config['key']) || !isset($config['columns'])) {
        throw new Exception("Missing required mapping fields for group: {$group_name}");
    }
}
```

## Performance Considerations

### 1. Batch Processing
- Use appropriate batch sizes (50-1000 records depending on data complexity)
- Monitor memory usage during large imports
- Consider breaking large files into smaller chunks

### 2. Database Optimization
- Ensure proper indexing on key columns
- Use appropriate data types for hash columns
- Consider disabling foreign key checks for large imports

### 3. Memory Usage
- The class processes data row by row to minimize memory footprint
- JSON flattening may increase memory usage for deeply nested structures
- Monitor PHP memory limits for large datasets

## Troubleshooting

### Common Issues and Solutions

1. **"Mapping Invalid" Error**
   - Check mapping array structure
   - Ensure all required fields are present
   - Validate array is countable

2. **Hash Generation Issues**
   - Verify unique_hash columns exist in source data
   - Check for null/empty values in hash columns
   - Ensure consistent data types

3. **Database Insert Failures**
   - Check table and column names in mapping
   - Verify database permissions
   - Review constraint violations in logs

4. **Memory Exhaustion**
   - Reduce batch sizes
   - Increase PHP memory limit
   - Consider file splitting for very large datasets

5. **JSON Path Resolution**
   - Use dot notation correctly (e.g., 'customer.address.street')
   - Check for array vs object structures in JSON
   - Verify path exists in source data

### Debug Mode Output

When debug mode is enabled, the class provides detailed logging:
- File reading progress
- Mapping group processing
- Hash generation details
- Database operation results
- Error details with context

### Log Analysis

Monitor logs for:
- Import progress and completion times
- Error patterns and frequencies
- Performance bottlenecks
- Data quality issues

## Integration Examples

### With Hilt Templates
```php
// Automatic table creation and data import for hilt templates
$table_name = "autobooks_{$route_key}_data";
$result = data_importer::import_csv_to_hilt_table(
    $table_name,
    $csv_file_path,
    true,
    $replace_existing
);
```

### With API Endpoints
```php
// In API controller
function import_data($params) {
    $mapping = get_mapping_configuration($params['type']);
    $result = data_importer::import_data_into_database(
        $mapping,
        $params['file_path'],
        $params['file_type'] ?? 'csv',
        $params['unique_fields'] ?? [],
        $params['debug'] ?? false
    );

    return $result;
}
```

This documentation provides comprehensive coverage of the `data_importer` class functionality, usage patterns, and best practices for effective data import operations.
